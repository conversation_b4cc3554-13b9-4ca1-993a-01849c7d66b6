import { NextResponse } from 'next/server';
import { getAllDocuments } from '@/lib/database';

/**
 * GET /api/documents - Get all documents
 */
export async function GET() {
  try {
    const documents = await getAllDocuments();
    
    return NextResponse.json({ 
      documents,
      count: documents.length 
    });
  } catch (error) {
    console.error('Error fetching documents:', error);
    return NextResponse.json(
      { error: 'Failed to fetch documents' },
      { status: 500 }
    );
  }
}
